# 📱 Pook - Dog Activity Tracking Webapp

Eine moderne Webanwendung für Hundebesitzer, mit der Familienmitglieder die Kotaktivitäten ihres Hundes dokumentieren und gemeinsam verwalten können.

## 🎯 Projektübersicht

Pook ist eine Full-Stack-Webanwendung, die es Familien ermöglicht, die Gesundheit und Aktivitäten ihrer Hunde zu verfolgen. Mit Fokus auf Benutzerfreundlichkeit und Datenschutz bietet die App eine sichere Plattform für die gemeinsame Haustierpflege.

## 🛠️ Tech-Stack

### Backend
- **Node.js** mit Express.js
- **MongoDB Atlas** als Datenbank
- **Google OAuth** für Authentifizierung
- **Multer** für Datei-Uploads
- **JWT** für Session-Management
- **Passport.js** für OAuth-Integration

### Frontend (geplant)
- **React** mit TailwindCSS
- **Vite** als Build-Tool
- **React Router** für Navigation
- **Dark Mode** Unterstützung

### Hosting
- **Backend**: Render/Heroku
- **Frontend**: Vercel
- **Datenbank**: MongoDB Atlas

## 🚀 Features

### ✅ Implementiert (Backend)
- **Google OAuth Authentifizierung**
- **Multi-Tenancy** (Familienverwaltung)
- **Rollenverwaltung** (Admin/Member)
- **Hundeprofile** mit detaillierten Informationen
- **Kot-Einträge** mit Fotos und Metadaten
- **Datei-Upload-System** mit Validierung
- **Statistiken und Analytics**
- **RESTful API** mit umfassender Fehlerbehandlung
- **Rate Limiting** und Sicherheitsmaßnahmen

### 🔄 In Entwicklung
- **React Frontend**
- **Push-Notifications**
- **Erweiterte Statistiken**
- **Mobile App** (zukünftig)

## 📁 Projektstruktur

```
pook/
├── backend/
│   ├── config/
│   │   └── passport.js          # Google OAuth Konfiguration
│   ├── middleware/
│   │   └── auth.js              # Authentifizierung & Autorisierung
│   ├── models/
│   │   ├── User.js              # Benutzer-Schema
│   │   ├── Family.js            # Familien-Schema
│   │   ├── Dog.js               # Hunde-Schema
│   │   └── PoopEntry.js         # Kot-Einträge Schema
│   ├── routes/
│   │   ├── auth.js              # Authentifizierung-Routen
│   │   ├── families.js          # Familienverwaltung
│   │   ├── dogs.js              # Hundeverwaltung
│   │   ├── entries.js           # Einträge-Management
│   │   └── upload.js            # Datei-Upload
│   ├── uploads/                 # Hochgeladene Dateien
│   ├── .env.example             # Umgebungsvariablen Vorlage
│   ├── package.json
│   └── server.js                # Haupt-Server-Datei
├── frontend/                    # (geplant)
├── .gitignore
└── README.md
```

## 🔧 Installation & Setup

### Voraussetzungen
- Node.js (v18+)
- MongoDB Atlas Account
- Google Cloud Console Account (für OAuth)

### Backend Setup

1. **Repository klonen**
   ```bash
   git clone <repository-url>
   cd pook
   ```

2. **Dependencies installieren**
   ```bash
   cd backend
   npm install
   ```

3. **Umgebungsvariablen konfigurieren**
   ```bash
   cp .env.example .env
   ```
   
   Bearbeite `.env` mit deinen Werten:
   ```env
   PORT=5000
   NODE_ENV=development
   MONGODB_URI=mongodb+srv://username:<EMAIL>/pook
   JWT_SECRET=your-super-secret-jwt-key
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   SESSION_SECRET=your-session-secret
   FRONTEND_URL=http://localhost:3000
   ```

4. **Google OAuth Setup**
   - Gehe zu [Google Cloud Console](https://console.cloud.google.com/)
   - Erstelle ein neues Projekt oder wähle ein bestehendes
   - Aktiviere die Google+ API
   - Erstelle OAuth 2.0 Credentials
   - Füge `http://localhost:5000/api/auth/google/callback` als Redirect URI hinzu

5. **MongoDB Atlas Setup**
   - Erstelle einen MongoDB Atlas Account
   - Erstelle ein neues Cluster
   - Erstelle einen Database User
   - Whitelist deine IP-Adresse
   - Kopiere die Connection String

6. **Server starten**
   ```bash
   npm run dev  # Entwicklung mit Nodemon
   # oder
   npm start    # Produktion
   ```

## 📚 API-Dokumentation

### Authentifizierung
- `GET /api/auth/google` - Google OAuth starten
- `GET /api/auth/google/callback` - OAuth Callback
- `GET /api/auth/me` - Aktuelle Benutzerinformationen
- `POST /api/auth/logout` - Abmelden

### Familien
- `GET /api/families` - Alle Familien des Benutzers
- `POST /api/families` - Neue Familie erstellen
- `POST /api/families/join` - Familie mit Einladungscode beitreten
- `PUT /api/families/:id` - Familie bearbeiten
- `DELETE /api/families/:id` - Familie löschen

### Hunde
- `GET /api/dogs` - Alle Hunde der aktuellen Familie
- `POST /api/dogs` - Neuen Hund hinzufügen
- `GET /api/dogs/:id` - Hundedetails
- `PUT /api/dogs/:id` - Hund bearbeiten
- `DELETE /api/dogs/:id` - Hund löschen

### Einträge
- `GET /api/entries` - Alle Einträge der Familie
- `POST /api/entries` - Neuen Eintrag erstellen
- `GET /api/entries/:id` - Eintrag-Details
- `PUT /api/entries/:id` - Eintrag bearbeiten
- `DELETE /api/entries/:id` - Eintrag löschen

### Upload
- `POST /api/upload/images` - Bilder hochladen
- `POST /api/upload/avatar` - Avatar hochladen
- `DELETE /api/upload/:familyId/:filename` - Datei löschen

## 🔒 Sicherheit

- **Google OAuth** für sichere Authentifizierung
- **JWT Tokens** für Session-Management
- **Rate Limiting** zum Schutz vor Missbrauch
- **Input Validation** für alle API-Endpunkte
- **File Upload Validation** (nur Bilder, Größenbegrenzung)
- **Multi-Tenancy** für Datenisolation zwischen Familien
- **CORS** Konfiguration für Frontend-Integration

## 🎨 Datenmodell

### User
- Google OAuth Integration
- Familienmitgliedschaften mit Rollen
- Benutzereinstellungen und Präferenzen

### Family
- Multi-Tenancy Support
- Einladungscode-System
- Rollenverwaltung (Admin/Member)
- Familieneinstellungen

### Dog
- Detaillierte Hundeinformationen
- Medizinische Daten
- Charakteristiken und Statistiken

### PoopEntry
- Zeitstempel und Fotos
- Qualitätsbewertung (Konsistenz, Farbe, Größe)
- Gesundheitsindikatoren
- Standortdaten und Notizen

## 🚀 Deployment

### Backend (Render/Heroku)
1. Repository mit Hosting-Provider verbinden
2. Umgebungsvariablen konfigurieren
3. Build-Befehle setzen: `npm install`
4. Start-Befehl setzen: `npm start`

### Frontend (Vercel) - geplant
1. React-App erstellen
2. Mit Vercel verbinden
3. Automatische Deployments konfigurieren

## 🤝 Beitragen

1. Fork das Repository
2. Erstelle einen Feature-Branch (`git checkout -b feature/AmazingFeature`)
3. Committe deine Änderungen (`git commit -m 'Add some AmazingFeature'`)
4. Push zum Branch (`git push origin feature/AmazingFeature`)
5. Öffne einen Pull Request

## 📝 Lizenz

Dieses Projekt ist unter der MIT-Lizenz lizenziert - siehe die [LICENSE](LICENSE) Datei für Details.

## 👨‍💻 Autor

**Kevin** - Entwickler und Hundeliebhaber

## 🐕 Warum Pook?

Als Hundebesitzer wissen wir, wie wichtig es ist, die Gesundheit unserer vierbeinigen Familienmitglieder im Auge zu behalten. Pook macht es einfach, wichtige Gesundheitsdaten zu verfolgen und mit der Familie zu teilen, damit jeder zur Pflege beitragen kann.

---

**Happy Tracking! 🐾**
