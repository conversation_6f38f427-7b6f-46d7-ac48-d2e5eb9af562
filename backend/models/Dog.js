const mongoose = require('mongoose');

const dogSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  breed: {
    type: String,
    trim: true,
    maxlength: 100
  },
  age: {
    type: Number,
    min: 0,
    max: 30
  },
  weight: {
    type: Number,
    min: 0,
    max: 200 // kg
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'unknown'],
    default: 'unknown'
  },
  color: {
    type: String,
    trim: true,
    maxlength: 50
  },
  avatar: {
    type: String,
    default: ''
  },
  family: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Family',
    required: true
  },
  addedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  medicalInfo: {
    allergies: [{
      type: String,
      trim: true
    }],
    medications: [{
      name: {
        type: String,
        required: true,
        trim: true
      },
      dosage: {
        type: String,
        trim: true
      },
      frequency: {
        type: String,
        trim: true
      },
      startDate: Date,
      endDate: Date
    }],
    vetInfo: {
      name: {
        type: String,
        trim: true
      },
      phone: {
        type: String,
        trim: true
      },
      address: {
        type: String,
        trim: true
      }
    },
    lastCheckup: Date,
    nextCheckup: Date
  },
  characteristics: {
    personality: [{
      type: String,
      trim: true
    }],
    activityLevel: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    size: {
      type: String,
      enum: ['small', 'medium', 'large', 'extra-large'],
      default: 'medium'
    }
  },
  statistics: {
    totalEntries: {
      type: Number,
      default: 0
    },
    lastEntry: {
      type: Date
    },
    averageEntriesPerDay: {
      type: Number,
      default: 0
    },
    longestStreak: {
      type: Number,
      default: 0
    },
    currentStreak: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster queries
dogSchema.index({ family: 1 });
dogSchema.index({ name: 1, family: 1 });
dogSchema.index({ createdAt: -1 });

// Virtual for age in human years (rough calculation)
dogSchema.virtual('ageInHumanYears').get(function() {
  if (!this.age) return null;
  
  // Simple calculation: first year = 15 human years, second year = 9, then 5 per year
  if (this.age <= 1) return this.age * 15;
  if (this.age <= 2) return 15 + (this.age - 1) * 9;
  return 15 + 9 + (this.age - 2) * 5;
});

// Method to update statistics
dogSchema.methods.updateStatistics = async function() {
  const PoopEntry = mongoose.model('PoopEntry');
  
  // Get total entries
  const totalEntries = await PoopEntry.countDocuments({ 
    dog: this._id,
    isActive: true 
  });
  
  // Get last entry
  const lastEntry = await PoopEntry.findOne({ 
    dog: this._id,
    isActive: true 
  }).sort({ createdAt: -1 });
  
  // Calculate average entries per day (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const recentEntries = await PoopEntry.countDocuments({
    dog: this._id,
    isActive: true,
    createdAt: { $gte: thirtyDaysAgo }
  });
  
  const averageEntriesPerDay = recentEntries / 30;
  
  // Update statistics
  this.statistics.totalEntries = totalEntries;
  this.statistics.lastEntry = lastEntry ? lastEntry.createdAt : null;
  this.statistics.averageEntriesPerDay = Math.round(averageEntriesPerDay * 100) / 100;
  
  return this.save();
};

// Method to calculate current streak
dogSchema.methods.calculateCurrentStreak = async function() {
  const PoopEntry = mongoose.model('PoopEntry');
  
  const entries = await PoopEntry.find({ 
    dog: this._id,
    isActive: true 
  }).sort({ createdAt: -1 }).limit(30);
  
  if (entries.length === 0) {
    this.statistics.currentStreak = 0;
    return this.save();
  }
  
  let streak = 0;
  let currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);
  
  for (const entry of entries) {
    const entryDate = new Date(entry.createdAt);
    entryDate.setHours(0, 0, 0, 0);
    
    const daysDiff = Math.floor((currentDate - entryDate) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === streak) {
      streak++;
      currentDate.setDate(currentDate.getDate() - 1);
    } else {
      break;
    }
  }
  
  this.statistics.currentStreak = streak;
  return this.save();
};

// Static method to find dogs by family
dogSchema.statics.findByFamily = function(familyId) {
  return this.find({ family: familyId, isActive: true }).sort({ name: 1 });
};

dogSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Dog', dogSchema);
