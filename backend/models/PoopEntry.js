const mongoose = require('mongoose');

const poopEntrySchema = new mongoose.Schema({
  dog: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dog',
    required: true
  },
  family: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Family',
    required: true
  },
  recordedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  timestamp: {
    type: Date,
    required: true,
    default: Date.now
  },
  photos: [{
    url: {
      type: String,
      required: true
    },
    filename: {
      type: String,
      required: true
    },
    size: {
      type: Number
    },
    mimeType: {
      type: String
    }
  }],
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [0, 0]
    },
    address: {
      type: String,
      trim: true
    }
  },
  quality: {
    consistency: {
      type: String,
      enum: ['liquid', 'soft', 'normal', 'hard', 'very-hard'],
      default: 'normal'
    },
    color: {
      type: String,
      enum: ['brown', 'dark-brown', 'light-brown', 'yellow', 'green', 'black', 'red', 'other'],
      default: 'brown'
    },
    size: {
      type: String,
      enum: ['small', 'medium', 'large', 'extra-large'],
      default: 'medium'
    },
    smell: {
      type: String,
      enum: ['normal', 'mild', 'strong', 'very-strong'],
      default: 'normal'
    }
  },
  healthIndicators: {
    blood: {
      type: Boolean,
      default: false
    },
    mucus: {
      type: Boolean,
      default: false
    },
    parasites: {
      type: Boolean,
      default: false
    },
    foreignObjects: {
      type: Boolean,
      default: false
    }
  },
  weather: {
    temperature: Number,
    condition: {
      type: String,
      enum: ['sunny', 'cloudy', 'rainy', 'snowy', 'windy', 'other']
    }
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isApproved: {
    type: Boolean,
    default: true
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  isActive: {
    type: Boolean,
    default: true
  },
  editHistory: [{
    editedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    editedAt: {
      type: Date,
      default: Date.now
    },
    changes: {
      type: mongoose.Schema.Types.Mixed
    },
    reason: {
      type: String,
      trim: true
    }
  }]
}, {
  timestamps: true
});

// Indexes for faster queries
poopEntrySchema.index({ dog: 1, timestamp: -1 });
poopEntrySchema.index({ family: 1, timestamp: -1 });
poopEntrySchema.index({ recordedBy: 1, timestamp: -1 });
poopEntrySchema.index({ timestamp: -1 });
poopEntrySchema.index({ 'location.coordinates': '2dsphere' });
poopEntrySchema.index({ tags: 1 });

// Virtual for entry age
poopEntrySchema.virtual('age').get(function() {
  return Date.now() - this.timestamp;
});

// Virtual for formatted timestamp
poopEntrySchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toLocaleString();
});

// Method to add photo
poopEntrySchema.methods.addPhoto = function(photoData) {
  this.photos.push(photoData);
  return this.save();
};

// Method to remove photo
poopEntrySchema.methods.removePhoto = function(photoId) {
  this.photos = this.photos.filter(photo => 
    photo._id.toString() !== photoId.toString()
  );
  return this.save();
};

// Method to add edit history
poopEntrySchema.methods.addEditHistory = function(editedBy, changes, reason = '') {
  this.editHistory.push({
    editedBy,
    changes,
    reason
  });
  return this.save();
};

// Method to check if entry needs approval
poopEntrySchema.methods.needsApproval = async function() {
  const Family = mongoose.model('Family');
  const family = await Family.findById(this.family);
  return family && family.settings.requireApprovalForEntries;
};

// Method to approve entry
poopEntrySchema.methods.approve = function(approvedBy) {
  this.isApproved = true;
  this.approvedBy = approvedBy;
  this.approvedAt = new Date();
  return this.save();
};

// Static method to get entries by date range
poopEntrySchema.statics.getByDateRange = function(familyId, startDate, endDate, dogId = null) {
  const query = {
    family: familyId,
    timestamp: {
      $gte: startDate,
      $lte: endDate
    },
    isActive: true
  };
  
  if (dogId) {
    query.dog = dogId;
  }
  
  return this.find(query)
    .populate('dog', 'name avatar')
    .populate('recordedBy', 'name avatar')
    .sort({ timestamp: -1 });
};

// Static method to get statistics
poopEntrySchema.statics.getStatistics = async function(familyId, dogId = null, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const matchQuery = {
    family: familyId,
    timestamp: { $gte: startDate },
    isActive: true
  };
  
  if (dogId) {
    matchQuery.dog = dogId;
  }
  
  const stats = await this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalEntries: { $sum: 1 },
        avgPerDay: { $avg: 1 },
        consistencyStats: {
          $push: '$quality.consistency'
        },
        colorStats: {
          $push: '$quality.color'
        },
        sizeStats: {
          $push: '$quality.size'
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalEntries: 0,
    avgPerDay: 0,
    consistencyStats: [],
    colorStats: [],
    sizeStats: []
  };
};

// Pre-save middleware to update dog statistics
poopEntrySchema.post('save', async function() {
  if (this.isNew) {
    const Dog = mongoose.model('Dog');
    const dog = await Dog.findById(this.dog);
    if (dog) {
      await dog.updateStatistics();
    }
  }
});

// Pre-remove middleware to update dog statistics
poopEntrySchema.post('findOneAndUpdate', async function() {
  if (this.getUpdate().$set && this.getUpdate().$set.isActive === false) {
    const Dog = mongoose.model('Dog');
    const entry = await this.model.findOne(this.getQuery());
    if (entry) {
      const dog = await Dog.findById(entry.dog);
      if (dog) {
        await dog.updateStatistics();
      }
    }
  }
});

poopEntrySchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('PoopEntry', poopEntrySchema);
