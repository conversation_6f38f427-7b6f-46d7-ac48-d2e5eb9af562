const mongoose = require('mongoose');

const familySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  inviteCode: {
    type: String,
    unique: true,
    required: true
  },
  members: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['admin', 'member'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    invitedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  dogs: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dog'
  }],
  settings: {
    allowMemberInvites: {
      type: Boolean,
      default: false
    },
    requireApprovalForEntries: {
      type: Boolean,
      default: false
    },
    notificationSettings: {
      newEntry: {
        type: <PERSON>olean,
        default: true
      },
      newMember: {
        type: Boolean,
        default: true
      },
      inactivityReminder: {
        type: Boolean,
        default: true
      }
    }
  },
  statistics: {
    totalEntries: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now
    },
    mostActiveUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster queries
familySchema.index({ inviteCode: 1 });
familySchema.index({ 'members.user': 1 });
familySchema.index({ createdAt: -1 });

// Pre-save middleware to generate invite code
familySchema.pre('save', function(next) {
  if (this.isNew && !this.inviteCode) {
    this.inviteCode = generateInviteCode();
  }
  next();
});

// Method to add member
familySchema.methods.addMember = function(userId, role = 'member', invitedBy = null) {
  // Check if user is already a member
  const existingMember = this.members.find(m => 
    m.user.toString() === userId.toString()
  );
  
  if (existingMember) {
    throw new Error('User is already a member of this family');
  }
  
  this.members.push({
    user: userId,
    role: role,
    invitedBy: invitedBy
  });
  
  return this.save();
};

// Method to remove member
familySchema.methods.removeMember = function(userId) {
  this.members = this.members.filter(m => 
    m.user.toString() !== userId.toString()
  );
  return this.save();
};

// Method to update member role
familySchema.methods.updateMemberRole = function(userId, newRole) {
  const member = this.members.find(m => 
    m.user.toString() === userId.toString()
  );
  
  if (!member) {
    throw new Error('User is not a member of this family');
  }
  
  member.role = newRole;
  return this.save();
};

// Method to check if user is admin
familySchema.methods.isAdmin = function(userId) {
  const member = this.members.find(m => 
    m.user.toString() === userId.toString()
  );
  return member && member.role === 'admin';
};

// Method to check if user is member
familySchema.methods.isMember = function(userId) {
  return this.members.some(m => 
    m.user.toString() === userId.toString()
  );
};

// Method to get admin count
familySchema.methods.getAdminCount = function() {
  return this.members.filter(m => m.role === 'admin').length;
};

// Static method to find by invite code
familySchema.statics.findByInviteCode = function(code) {
  return this.findOne({ inviteCode: code, isActive: true });
};

// Generate random invite code
function generateInviteCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

module.exports = mongoose.model('Family', familySchema);
