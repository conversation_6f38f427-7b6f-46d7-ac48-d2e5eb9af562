const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Family = require('../models/Family');

// Middleware to check if user is authenticated
const requireAuth = async (req, res, next) => {
  try {
    // Check for session-based authentication (Passport)
    if (req.isAuthenticated && req.isAuthenticated()) {
      req.user = req.user;
      return next();
    }
    
    // Check for JWT token in headers
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId).populate('currentFamily');
      
      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'Invalid token. User not found or inactive.'
        });
      }
      
      req.user = user;
      next();
    } catch (jwtError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.'
      });
    }
    
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during authentication'
    });
  }
};

// Middleware to check if user has a current family
const requireFamily = async (req, res, next) => {
  try {
    if (!req.user.currentFamily) {
      return res.status(403).json({
        success: false,
        message: 'You must be part of a family to access this resource.'
      });
    }
    
    // Verify the family still exists and user is still a member
    const family = await Family.findById(req.user.currentFamily);
    
    if (!family || !family.isActive) {
      // Clear the user's current family if it doesn't exist
      req.user.currentFamily = null;
      await req.user.save();
      
      return res.status(403).json({
        success: false,
        message: 'Family not found or inactive.'
      });
    }
    
    if (!family.isMember(req.user._id)) {
      // Clear the user's current family if they're no longer a member
      req.user.currentFamily = null;
      await req.user.save();
      
      return res.status(403).json({
        success: false,
        message: 'You are no longer a member of this family.'
      });
    }
    
    req.family = family;
    next();
  } catch (error) {
    console.error('Family middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during family verification'
    });
  }
};

// Middleware to check if user is admin of current family
const requireFamilyAdmin = async (req, res, next) => {
  try {
    if (!req.family) {
      return res.status(403).json({
        success: false,
        message: 'Family context required.'
      });
    }
    
    if (!req.family.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required for this action.'
      });
    }
    
    next();
  } catch (error) {
    console.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during admin verification'
    });
  }
};

// Middleware to check if user can access specific family
const requireFamilyAccess = (paramName = 'familyId') => {
  return async (req, res, next) => {
    try {
      const familyId = req.params[paramName] || req.body.familyId || req.query.familyId;
      
      if (!familyId) {
        return res.status(400).json({
          success: false,
          message: 'Family ID is required.'
        });
      }
      
      const family = await Family.findById(familyId);
      
      if (!family || !family.isActive) {
        return res.status(404).json({
          success: false,
          message: 'Family not found.'
        });
      }
      
      if (!family.isMember(req.user._id)) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this family.'
        });
      }
      
      req.targetFamily = family;
      next();
    } catch (error) {
      console.error('Family access middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during family access verification'
      });
    }
  };
};

// Middleware to validate family ownership of resource
const requireResourceOwnership = (resourceModel, resourceParam = 'id') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params[resourceParam];
      const Model = require(`../models/${resourceModel}`);
      
      const resource = await Model.findById(resourceId);
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          message: `${resourceModel} not found.`
        });
      }
      
      // Check if resource belongs to user's current family
      if (resource.family.toString() !== req.user.currentFamily.toString()) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this resource.'
        });
      }
      
      req.resource = resource;
      next();
    } catch (error) {
      console.error('Resource ownership middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during resource ownership verification'
      });
    }
  };
};

// Middleware to check rate limiting per user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user._id.toString();
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old requests
    if (requests.has(userId)) {
      const userRequests = requests.get(userId).filter(time => time > windowStart);
      requests.set(userId, userRequests);
    } else {
      requests.set(userId, []);
    }
    
    const userRequests = requests.get(userId);
    
    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests. Please try again later.'
      });
    }
    
    userRequests.push(now);
    next();
  };
};

module.exports = {
  requireAuth,
  requireFamily,
  requireFamilyAdmin,
  requireFamilyAccess,
  requireResourceOwnership,
  userRateLimit
};
