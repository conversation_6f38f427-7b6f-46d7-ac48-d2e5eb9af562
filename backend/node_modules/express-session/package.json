{"name": "express-session", "version": "1.18.1", "description": "Simple session middleware for Express", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "expressjs/session", "license": "MIT", "dependencies": {"cookie": "0.7.2", "cookie-signature": "1.0.7", "debug": "2.6.9", "depd": "~2.0.0", "on-headers": "~1.0.2", "parseurl": "~1.3.3", "safe-buffer": "5.2.1", "uid-safe": "~2.1.5"}, "devDependencies": {"after": "0.8.2", "cookie-parser": "1.4.6", "eslint": "8.56.0", "eslint-plugin-markdown": "3.0.1", "express": "4.17.3", "mocha": "10.2.0", "nyc": "15.1.0", "supertest": "6.3.4"}, "files": ["session/", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint . && node ./scripts/lint-readme.js", "test": "mocha --require test/support/env --check-leaks --bail --no-exit --reporter spec test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}}