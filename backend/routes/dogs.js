const express = require('express');
const { requireAuth, requireFamily, requireResourceOwnership } = require('../middleware/auth');
const Dog = require('../models/Dog');
const PoopEntry = require('../models/PoopEntry');

const router = express.Router();

// Apply auth middleware to all routes
router.use(requireAuth);
router.use(requireFamily);

// @route   GET /api/dogs
// @desc    Get all dogs in current family
// @access  Private (family member)
router.get('/', async (req, res) => {
  try {
    const dogs = await Dog.findByFamily(req.user.currentFamily)
      .populate('addedBy', 'name avatar')
      .sort({ name: 1 });

    res.json({
      success: true,
      dogs
    });
  } catch (error) {
    console.error('Get dogs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dogs'
    });
  }
});

// @route   POST /api/dogs
// @desc    Add new dog to family
// @access  Private (family member)
router.post('/', async (req, res) => {
  try {
    const {
      name,
      breed,
      age,
      weight,
      gender,
      color,
      avatar,
      medicalInfo,
      characteristics
    } = req.body;

    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Dog name is required'
      });
    }

    // Check if dog name already exists in family
    const existingDog = await Dog.findOne({
      family: req.user.currentFamily,
      name: name.trim(),
      isActive: true
    });

    if (existingDog) {
      return res.status(400).json({
        success: false,
        message: 'A dog with this name already exists in your family'
      });
    }

    const dog = new Dog({
      name: name.trim(),
      breed: breed?.trim(),
      age,
      weight,
      gender,
      color: color?.trim(),
      avatar: avatar || '',
      family: req.user.currentFamily,
      addedBy: req.user._id,
      medicalInfo: medicalInfo || {},
      characteristics: characteristics || {}
    });

    await dog.save();

    // Add dog to family's dogs array
    req.family.dogs.push(dog._id);
    await req.family.save();

    const populatedDog = await Dog.findById(dog._id)
      .populate('addedBy', 'name avatar');

    res.status(201).json({
      success: true,
      message: 'Dog added successfully',
      dog: populatedDog
    });
  } catch (error) {
    console.error('Add dog error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add dog'
    });
  }
});

// @route   GET /api/dogs/:id
// @desc    Get dog details
// @access  Private (family member)
router.get('/:id', requireResourceOwnership('Dog'), async (req, res) => {
  try {
    const dog = await Dog.findById(req.params.id)
      .populate('addedBy', 'name avatar')
      .populate('family', 'name');

    if (!dog || !dog.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Dog not found'
      });
    }

    // Get recent entries for this dog
    const recentEntries = await PoopEntry.find({
      dog: dog._id,
      isActive: true
    })
      .populate('recordedBy', 'name avatar')
      .sort({ timestamp: -1 })
      .limit(10);

    res.json({
      success: true,
      dog: {
        ...dog.toObject(),
        recentEntries
      }
    });
  } catch (error) {
    console.error('Get dog error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dog details'
    });
  }
});

// @route   PUT /api/dogs/:id
// @desc    Update dog
// @access  Private (family member)
router.put('/:id', requireResourceOwnership('Dog'), async (req, res) => {
  try {
    const {
      name,
      breed,
      age,
      weight,
      gender,
      color,
      avatar,
      medicalInfo,
      characteristics
    } = req.body;

    const updateData = {};
    
    if (name && name.trim().length > 0) {
      // Check if new name conflicts with existing dogs
      const existingDog = await Dog.findOne({
        family: req.user.currentFamily,
        name: name.trim(),
        isActive: true,
        _id: { $ne: req.params.id }
      });

      if (existingDog) {
        return res.status(400).json({
          success: false,
          message: 'A dog with this name already exists in your family'
        });
      }

      updateData.name = name.trim();
    }

    if (breed !== undefined) updateData.breed = breed?.trim();
    if (age !== undefined) updateData.age = age;
    if (weight !== undefined) updateData.weight = weight;
    if (gender !== undefined) updateData.gender = gender;
    if (color !== undefined) updateData.color = color?.trim();
    if (avatar !== undefined) updateData.avatar = avatar;
    
    if (medicalInfo) {
      updateData.medicalInfo = { ...req.resource.medicalInfo, ...medicalInfo };
    }
    
    if (characteristics) {
      updateData.characteristics = { ...req.resource.characteristics, ...characteristics };
    }

    const dog = await Dog.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('addedBy', 'name avatar');

    res.json({
      success: true,
      message: 'Dog updated successfully',
      dog
    });
  } catch (error) {
    console.error('Update dog error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update dog'
    });
  }
});

// @route   DELETE /api/dogs/:id
// @desc    Delete dog (soft delete)
// @access  Private (family member)
router.delete('/:id', requireResourceOwnership('Dog'), async (req, res) => {
  try {
    // Soft delete dog
    await Dog.findByIdAndUpdate(req.params.id, { isActive: false });

    // Remove dog from family's dogs array
    req.family.dogs = req.family.dogs.filter(dogId => 
      dogId.toString() !== req.params.id
    );
    await req.family.save();

    // Soft delete all entries for this dog
    await PoopEntry.updateMany(
      { dog: req.params.id },
      { isActive: false }
    );

    res.json({
      success: true,
      message: 'Dog deleted successfully'
    });
  } catch (error) {
    console.error('Delete dog error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete dog'
    });
  }
});

// @route   GET /api/dogs/:id/statistics
// @desc    Get dog statistics
// @access  Private (family member)
router.get('/:id/statistics', requireResourceOwnership('Dog'), async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const daysNum = parseInt(days);

    // Update dog statistics first
    await req.resource.updateStatistics();
    await req.resource.calculateCurrentStreak();

    // Get detailed statistics
    const stats = await PoopEntry.getStatistics(req.user.currentFamily, req.params.id, daysNum);

    // Get entries by day for chart data
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysNum);

    const entriesByDay = await PoopEntry.aggregate([
      {
        $match: {
          dog: req.resource._id,
          family: req.user.currentFamily,
          timestamp: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$timestamp" }
          },
          count: { $sum: 1 },
          entries: { $push: "$$ROOT" }
        }
      },
      {
        $sort: { "_id": 1 }
      }
    ]);

    // Get quality distribution
    const qualityStats = await PoopEntry.aggregate([
      {
        $match: {
          dog: req.resource._id,
          family: req.user.currentFamily,
          timestamp: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: null,
          consistencyDistribution: {
            $push: "$quality.consistency"
          },
          colorDistribution: {
            $push: "$quality.color"
          },
          sizeDistribution: {
            $push: "$quality.size"
          }
        }
      }
    ]);

    res.json({
      success: true,
      statistics: {
        ...req.resource.statistics,
        period: `${daysNum} days`,
        entriesByDay,
        qualityDistribution: qualityStats[0] || {
          consistencyDistribution: [],
          colorDistribution: [],
          sizeDistribution: []
        },
        ...stats
      }
    });
  } catch (error) {
    console.error('Get dog statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dog statistics'
    });
  }
});

// @route   GET /api/dogs/:id/entries
// @desc    Get dog's poop entries
// @access  Private (family member)
router.get('/:id/entries', requireResourceOwnership('Dog'), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      startDate, 
      endDate,
      consistency,
      color,
      size
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    const query = {
      dog: req.params.id,
      family: req.user.currentFamily,
      isActive: true
    };

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    if (consistency) query['quality.consistency'] = consistency;
    if (color) query['quality.color'] = color;
    if (size) query['quality.size'] = size;

    const [entries, total] = await Promise.all([
      PoopEntry.find(query)
        .populate('recordedBy', 'name avatar')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limitNum),
      PoopEntry.countDocuments(query)
    ]);

    res.json({
      success: true,
      entries,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get dog entries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dog entries'
    });
  }
});

module.exports = router;
