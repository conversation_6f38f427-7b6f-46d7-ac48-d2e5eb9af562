const express = require('express');
const { requireAuth, requireFamily, requireResourceOwnership } = require('../middleware/auth');
const PoopEntry = require('../models/PoopEntry');
const Dog = require('../models/Dog');

const router = express.Router();

// Apply auth middleware to all routes
router.use(requireAuth);
router.use(requireFamily);

// @route   GET /api/entries
// @desc    Get poop entries for current family
// @access  Private (family member)
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      dogId,
      startDate, 
      endDate,
      consistency,
      color,
      size,
      recordedBy
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    const query = {
      family: req.user.currentFamily,
      isActive: true
    };

    if (dogId) query.dog = dogId;
    if (recordedBy) query.recordedBy = recordedBy;

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    if (consistency) query['quality.consistency'] = consistency;
    if (color) query['quality.color'] = color;
    if (size) query['quality.size'] = size;

    const [entries, total] = await Promise.all([
      PoopEntry.find(query)
        .populate('dog', 'name avatar')
        .populate('recordedBy', 'name avatar')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limitNum),
      PoopEntry.countDocuments(query)
    ]);

    res.json({
      success: true,
      entries,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get entries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get entries'
    });
  }
});

// @route   POST /api/entries
// @desc    Create new poop entry
// @access  Private (family member)
router.post('/', async (req, res) => {
  try {
    const {
      dogId,
      timestamp,
      photos,
      description,
      location,
      quality,
      healthIndicators,
      weather,
      notes,
      tags
    } = req.body;

    if (!dogId) {
      return res.status(400).json({
        success: false,
        message: 'Dog ID is required'
      });
    }

    // Verify dog belongs to current family
    const dog = await Dog.findOne({
      _id: dogId,
      family: req.user.currentFamily,
      isActive: true
    });

    if (!dog) {
      return res.status(404).json({
        success: false,
        message: 'Dog not found in your family'
      });
    }

    const entry = new PoopEntry({
      dog: dogId,
      family: req.user.currentFamily,
      recordedBy: req.user._id,
      timestamp: timestamp ? new Date(timestamp) : new Date(),
      photos: photos || [],
      description: description?.trim(),
      location: location || {},
      quality: quality || {},
      healthIndicators: healthIndicators || {},
      weather: weather || {},
      notes: notes?.trim(),
      tags: tags || []
    });

    // Check if entry needs approval
    const needsApproval = await entry.needsApproval();
    if (needsApproval) {
      entry.isApproved = false;
    }

    await entry.save();

    // Update family statistics
    req.family.statistics.totalEntries += 1;
    req.family.statistics.lastActivity = new Date();
    await req.family.save();

    const populatedEntry = await PoopEntry.findById(entry._id)
      .populate('dog', 'name avatar')
      .populate('recordedBy', 'name avatar');

    res.status(201).json({
      success: true,
      message: 'Entry created successfully',
      entry: populatedEntry
    });
  } catch (error) {
    console.error('Create entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create entry'
    });
  }
});

// @route   GET /api/entries/:id
// @desc    Get specific entry
// @access  Private (family member)
router.get('/:id', requireResourceOwnership('PoopEntry'), async (req, res) => {
  try {
    const entry = await PoopEntry.findById(req.params.id)
      .populate('dog', 'name avatar breed')
      .populate('recordedBy', 'name avatar')
      .populate('approvedBy', 'name avatar')
      .populate('editHistory.editedBy', 'name avatar');

    if (!entry || !entry.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Entry not found'
      });
    }

    res.json({
      success: true,
      entry
    });
  } catch (error) {
    console.error('Get entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get entry'
    });
  }
});

// @route   PUT /api/entries/:id
// @desc    Update entry
// @access  Private (family member)
router.put('/:id', requireResourceOwnership('PoopEntry'), async (req, res) => {
  try {
    const {
      timestamp,
      photos,
      description,
      location,
      quality,
      healthIndicators,
      weather,
      notes,
      tags,
      editReason
    } = req.body;

    const originalEntry = { ...req.resource.toObject() };
    const changes = {};

    // Track changes
    if (timestamp && new Date(timestamp).getTime() !== req.resource.timestamp.getTime()) {
      changes.timestamp = { from: req.resource.timestamp, to: new Date(timestamp) };
      req.resource.timestamp = new Date(timestamp);
    }

    if (photos && JSON.stringify(photos) !== JSON.stringify(req.resource.photos)) {
      changes.photos = { from: req.resource.photos, to: photos };
      req.resource.photos = photos;
    }

    if (description !== undefined && description !== req.resource.description) {
      changes.description = { from: req.resource.description, to: description };
      req.resource.description = description?.trim();
    }

    if (location && JSON.stringify(location) !== JSON.stringify(req.resource.location)) {
      changes.location = { from: req.resource.location, to: location };
      req.resource.location = { ...req.resource.location, ...location };
    }

    if (quality && JSON.stringify(quality) !== JSON.stringify(req.resource.quality)) {
      changes.quality = { from: req.resource.quality, to: quality };
      req.resource.quality = { ...req.resource.quality, ...quality };
    }

    if (healthIndicators && JSON.stringify(healthIndicators) !== JSON.stringify(req.resource.healthIndicators)) {
      changes.healthIndicators = { from: req.resource.healthIndicators, to: healthIndicators };
      req.resource.healthIndicators = { ...req.resource.healthIndicators, ...healthIndicators };
    }

    if (weather && JSON.stringify(weather) !== JSON.stringify(req.resource.weather)) {
      changes.weather = { from: req.resource.weather, to: weather };
      req.resource.weather = { ...req.resource.weather, ...weather };
    }

    if (notes !== undefined && notes !== req.resource.notes) {
      changes.notes = { from: req.resource.notes, to: notes };
      req.resource.notes = notes?.trim();
    }

    if (tags && JSON.stringify(tags) !== JSON.stringify(req.resource.tags)) {
      changes.tags = { from: req.resource.tags, to: tags };
      req.resource.tags = tags;
    }

    // Add edit history if there are changes
    if (Object.keys(changes).length > 0) {
      await req.resource.addEditHistory(req.user._id, changes, editReason);
    }

    await req.resource.save();

    const populatedEntry = await PoopEntry.findById(req.params.id)
      .populate('dog', 'name avatar')
      .populate('recordedBy', 'name avatar');

    res.json({
      success: true,
      message: 'Entry updated successfully',
      entry: populatedEntry
    });
  } catch (error) {
    console.error('Update entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update entry'
    });
  }
});

// @route   DELETE /api/entries/:id
// @desc    Delete entry (soft delete)
// @access  Private (family member)
router.delete('/:id', requireResourceOwnership('PoopEntry'), async (req, res) => {
  try {
    // Soft delete entry
    await PoopEntry.findByIdAndUpdate(req.params.id, { isActive: false });

    // Update family statistics
    if (req.family.statistics.totalEntries > 0) {
      req.family.statistics.totalEntries -= 1;
      await req.family.save();
    }

    res.json({
      success: true,
      message: 'Entry deleted successfully'
    });
  } catch (error) {
    console.error('Delete entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete entry'
    });
  }
});

// @route   POST /api/entries/:id/approve
// @desc    Approve entry (for families with approval required)
// @access  Private (family admin)
router.post('/:id/approve', requireResourceOwnership('PoopEntry'), async (req, res) => {
  try {
    // Check if user is admin
    if (!req.family.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required to approve entries'
      });
    }

    if (req.resource.isApproved) {
      return res.status(400).json({
        success: false,
        message: 'Entry is already approved'
      });
    }

    await req.resource.approve(req.user._id);

    const populatedEntry = await PoopEntry.findById(req.params.id)
      .populate('dog', 'name avatar')
      .populate('recordedBy', 'name avatar')
      .populate('approvedBy', 'name avatar');

    res.json({
      success: true,
      message: 'Entry approved successfully',
      entry: populatedEntry
    });
  } catch (error) {
    console.error('Approve entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve entry'
    });
  }
});

// @route   GET /api/entries/statistics/family
// @desc    Get family statistics
// @access  Private (family member)
router.get('/statistics/family', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const daysNum = parseInt(days);

    const stats = await PoopEntry.getStatistics(req.user.currentFamily, null, daysNum);

    // Get entries by day for chart data
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysNum);

    const entriesByDay = await PoopEntry.aggregate([
      {
        $match: {
          family: req.user.currentFamily,
          timestamp: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$timestamp" }
          },
          count: { $sum: 1 },
          dogs: { $addToSet: "$dog" }
        }
      },
      {
        $sort: { "_id": 1 }
      }
    ]);

    // Get entries by dog
    const entriesByDog = await PoopEntry.aggregate([
      {
        $match: {
          family: req.user.currentFamily,
          timestamp: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: "$dog",
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: "dogs",
          localField: "_id",
          foreignField: "_id",
          as: "dog"
        }
      },
      {
        $unwind: "$dog"
      },
      {
        $project: {
          dogName: "$dog.name",
          dogAvatar: "$dog.avatar",
          count: 1
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get entries by user
    const entriesByUser = await PoopEntry.aggregate([
      {
        $match: {
          family: req.user.currentFamily,
          timestamp: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: "$recordedBy",
          count: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "user"
        }
      },
      {
        $unwind: "$user"
      },
      {
        $project: {
          userName: "$user.name",
          userAvatar: "$user.avatar",
          count: 1
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    res.json({
      success: true,
      statistics: {
        period: `${daysNum} days`,
        ...stats,
        entriesByDay,
        entriesByDog,
        entriesByUser
      }
    });
  } catch (error) {
    console.error('Get family statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get family statistics'
    });
  }
});

// @route   GET /api/entries/pending
// @desc    Get pending entries (for families with approval required)
// @access  Private (family admin)
router.get('/pending', async (req, res) => {
  try {
    // Check if user is admin
    if (!req.family.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required to view pending entries'
      });
    }

    const pendingEntries = await PoopEntry.find({
      family: req.user.currentFamily,
      isApproved: false,
      isActive: true
    })
      .populate('dog', 'name avatar')
      .populate('recordedBy', 'name avatar')
      .sort({ timestamp: -1 });

    res.json({
      success: true,
      entries: pendingEntries
    });
  } catch (error) {
    console.error('Get pending entries error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pending entries'
    });
  }
});

module.exports = router;
