const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { requireAuth, requireFamily, userRateLimit } = require('../middleware/auth');

const router = express.Router();

// Apply auth middleware to all routes
router.use(requireAuth);
router.use(requireFamily);

// Apply rate limiting for uploads
router.use(userRateLimit(50, 15 * 60 * 1000)); // 50 uploads per 15 minutes

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const familyDir = path.join(uploadsDir, req.user.currentFamily.toString());
    if (!fs.existsSync(familyDir)) {
      fs.mkdirSync(familyDir, { recursive: true });
    }
    cb(null, familyDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  }
});

// File filter for images only
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: 5 // Maximum 5 files per upload
  },
  fileFilter: fileFilter
});

// @route   POST /api/upload/images
// @desc    Upload images for poop entries
// @access  Private (family member)
router.post('/images', upload.array('images', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    const uploadedFiles = req.files.map(file => ({
      url: `/uploads/${req.user.currentFamily}/${file.filename}`,
      filename: file.filename,
      originalName: file.originalname,
      size: file.size,
      mimeType: file.mimetype,
      uploadedAt: new Date()
    }));

    res.json({
      success: true,
      message: `${uploadedFiles.length} file(s) uploaded successfully`,
      files: uploadedFiles
    });
  } catch (error) {
    console.error('Upload error:', error);
    
    // Clean up uploaded files if there was an error
    if (req.files) {
      req.files.forEach(file => {
        fs.unlink(file.path, (err) => {
          if (err) console.error('Error deleting file:', err);
        });
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Failed to upload files'
    });
  }
});

// @route   POST /api/upload/avatar
// @desc    Upload avatar for dog or user
// @access  Private (family member)
router.post('/avatar', upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const uploadedFile = {
      url: `/uploads/${req.user.currentFamily}/${req.file.filename}`,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      mimeType: req.file.mimetype,
      uploadedAt: new Date()
    };

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      file: uploadedFile
    });
  } catch (error) {
    console.error('Avatar upload error:', error);
    
    // Clean up uploaded file if there was an error
    if (req.file) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error deleting file:', err);
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Failed to upload avatar'
    });
  }
});

// @route   DELETE /api/upload/:familyId/:filename
// @desc    Delete uploaded file
// @access  Private (family member)
router.delete('/:familyId/:filename', async (req, res) => {
  try {
    const { familyId, filename } = req.params;

    // Verify user has access to this family
    if (familyId !== req.user.currentFamily.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You do not have access to delete files from this family'
      });
    }

    // Validate filename to prevent directory traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    const filePath = path.join(uploadsDir, familyId, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Delete file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('Error deleting file:', err);
        return res.status(500).json({
          success: false,
          message: 'Failed to delete file'
        });
      }

      res.json({
        success: true,
        message: 'File deleted successfully'
      });
    });
  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete file'
    });
  }
});

// @route   GET /api/upload/info/:familyId/:filename
// @desc    Get file information
// @access  Private (family member)
router.get('/info/:familyId/:filename', async (req, res) => {
  try {
    const { familyId, filename } = req.params;

    // Verify user has access to this family
    if (familyId !== req.user.currentFamily.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You do not have access to files from this family'
      });
    }

    // Validate filename
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }

    const filePath = path.join(uploadsDir, familyId, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    const ext = path.extname(filename).toLowerCase();
    
    // Determine mime type
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };

    res.json({
      success: true,
      file: {
        filename,
        size: stats.size,
        mimeType: mimeTypes[ext] || 'application/octet-stream',
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        url: `/uploads/${familyId}/${filename}`
      }
    });
  } catch (error) {
    console.error('Get file info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get file information'
    });
  }
});

// @route   GET /api/upload/family-files
// @desc    Get all files for current family
// @access  Private (family member)
router.get('/family-files', async (req, res) => {
  try {
    const familyDir = path.join(uploadsDir, req.user.currentFamily.toString());

    if (!fs.existsSync(familyDir)) {
      return res.json({
        success: true,
        files: []
      });
    }

    const files = fs.readdirSync(familyDir);
    const fileInfos = files.map(filename => {
      const filePath = path.join(familyDir, filename);
      const stats = fs.statSync(filePath);
      const ext = path.extname(filename).toLowerCase();
      
      const mimeTypes = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.webp': 'image/webp'
      };

      return {
        filename,
        size: stats.size,
        mimeType: mimeTypes[ext] || 'application/octet-stream',
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        url: `/uploads/${req.user.currentFamily}/${filename}`
      };
    });

    // Sort by creation date (newest first)
    fileInfos.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json({
      success: true,
      files: fileInfos
    });
  } catch (error) {
    console.error('Get family files error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get family files'
    });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 5MB.'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum is 5 files per upload.'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Unexpected field name for file upload.'
      });
    }
  }
  
  res.status(400).json({
    success: false,
    message: error.message || 'Upload error'
  });
});

module.exports = router;
