const express = require('express');
const { requireAuth, requireFamily, requireFamilyAdmin, requireFamilyAccess } = require('../middleware/auth');
const Family = require('../models/Family');
const User = require('../models/User');
const Dog = require('../models/Dog');

const router = express.Router();

// Apply auth middleware to all routes
router.use(requireAuth);

// @route   GET /api/families
// @desc    Get user's families
// @access  Private
router.get('/', async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate({
        path: 'families.family',
        select: 'name description inviteCode members dogs statistics createdAt',
        populate: {
          path: 'dogs',
          select: 'name avatar'
        }
      });

    const families = user.families.map(f => ({
      ...f.family.toObject(),
      userRole: f.role,
      joinedAt: f.joinedAt,
      memberCount: f.family.members.length,
      dogCount: f.family.dogs.length
    }));

    res.json({
      success: true,
      families
    });
  } catch (error) {
    console.error('Get families error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get families'
    });
  }
});

// @route   POST /api/families
// @desc    Create new family
// @access  Private
router.post('/', async (req, res) => {
  try {
    const { name, description } = req.body;

    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Family name is required'
      });
    }

    // Create family
    const family = new Family({
      name: name.trim(),
      description: description?.trim() || '',
      members: [{
        user: req.user._id,
        role: 'admin'
      }]
    });

    await family.save();

    // Add family to user's families list
    req.user.families.push({
      family: family._id,
      role: 'admin'
    });

    // Set as current family if user doesn't have one
    if (!req.user.currentFamily) {
      req.user.currentFamily = family._id;
    }

    await req.user.save();

    const populatedFamily = await Family.findById(family._id)
      .populate('members.user', 'name email avatar')
      .populate('dogs', 'name avatar');

    res.status(201).json({
      success: true,
      message: 'Family created successfully',
      family: populatedFamily
    });
  } catch (error) {
    console.error('Create family error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create family'
    });
  }
});

// @route   GET /api/families/:id
// @desc    Get family details
// @access  Private (family member)
router.get('/:id', requireFamilyAccess('id'), async (req, res) => {
  try {
    const family = await Family.findById(req.params.id)
      .populate('members.user', 'name email avatar lastLogin')
      .populate('members.invitedBy', 'name')
      .populate('dogs', 'name avatar breed age statistics')
      .populate('statistics.mostActiveUser', 'name avatar');

    res.json({
      success: true,
      family
    });
  } catch (error) {
    console.error('Get family error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get family details'
    });
  }
});

// @route   PUT /api/families/:id
// @desc    Update family
// @access  Private (family admin)
router.put('/:id', requireFamilyAccess('id'), async (req, res) => {
  try {
    const { name, description, settings } = req.body;

    // Check if user is admin
    if (!req.targetFamily.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required'
      });
    }

    const updateData = {};
    if (name) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description.trim();
    if (settings) updateData.settings = { ...req.targetFamily.settings, ...settings };

    const family = await Family.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('members.user', 'name email avatar');

    res.json({
      success: true,
      message: 'Family updated successfully',
      family
    });
  } catch (error) {
    console.error('Update family error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update family'
    });
  }
});

// @route   POST /api/families/:id/join
// @desc    Join family by invite code
// @access  Private
router.post('/join', async (req, res) => {
  try {
    const { inviteCode } = req.body;

    if (!inviteCode) {
      return res.status(400).json({
        success: false,
        message: 'Invite code is required'
      });
    }

    const family = await Family.findByInviteCode(inviteCode.toUpperCase());

    if (!family) {
      return res.status(404).json({
        success: false,
        message: 'Invalid invite code'
      });
    }

    // Check if user is already a member
    if (family.isMember(req.user._id)) {
      return res.status(400).json({
        success: false,
        message: 'You are already a member of this family'
      });
    }

    // Add user to family
    await family.addMember(req.user._id);

    // Add family to user's families list
    req.user.families.push({
      family: family._id,
      role: 'member'
    });

    // Set as current family if user doesn't have one
    if (!req.user.currentFamily) {
      req.user.currentFamily = family._id;
    }

    await req.user.save();

    const populatedFamily = await Family.findById(family._id)
      .populate('members.user', 'name email avatar')
      .populate('dogs', 'name avatar');

    res.json({
      success: true,
      message: 'Successfully joined family',
      family: populatedFamily
    });
  } catch (error) {
    console.error('Join family error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to join family'
    });
  }
});

// @route   POST /api/families/:id/invite
// @desc    Generate new invite code
// @access  Private (family admin)
router.post('/:id/invite', requireFamilyAccess('id'), async (req, res) => {
  try {
    // Check if user is admin or if members can invite
    const isAdmin = req.targetFamily.isAdmin(req.user._id);
    const canInvite = req.targetFamily.settings.allowMemberInvites;

    if (!isAdmin && !canInvite) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to generate invite codes'
      });
    }

    // Generate new invite code
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let newInviteCode;
    let isUnique = false;

    while (!isUnique) {
      newInviteCode = '';
      for (let i = 0; i < 8; i++) {
        newInviteCode += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      
      const existingFamily = await Family.findOne({ inviteCode: newInviteCode });
      if (!existingFamily) {
        isUnique = true;
      }
    }

    req.targetFamily.inviteCode = newInviteCode;
    await req.targetFamily.save();

    res.json({
      success: true,
      message: 'New invite code generated',
      inviteCode: newInviteCode
    });
  } catch (error) {
    console.error('Generate invite error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate invite code'
    });
  }
});

// @route   DELETE /api/families/:id/members/:userId
// @desc    Remove member from family
// @access  Private (family admin)
router.delete('/:id/members/:userId', requireFamilyAccess('id'), async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user is admin
    if (!req.targetFamily.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required'
      });
    }

    // Can't remove yourself if you're the only admin
    if (userId === req.user._id.toString()) {
      const adminCount = req.targetFamily.getAdminCount();
      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot remove the only admin. Promote another member to admin first.'
        });
      }
    }

    // Remove member from family
    await req.targetFamily.removeMember(userId);

    // Remove family from user's families list
    const user = await User.findById(userId);
    if (user) {
      user.families = user.families.filter(f => 
        f.family.toString() !== req.targetFamily._id.toString()
      );
      
      // Clear current family if it was this family
      if (user.currentFamily && user.currentFamily.toString() === req.targetFamily._id.toString()) {
        user.currentFamily = user.families.length > 0 ? user.families[0].family : null;
      }
      
      await user.save();
    }

    res.json({
      success: true,
      message: 'Member removed successfully'
    });
  } catch (error) {
    console.error('Remove member error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove member'
    });
  }
});

// @route   PUT /api/families/:id/members/:userId/role
// @desc    Update member role
// @access  Private (family admin)
router.put('/:id/members/:userId/role', requireFamilyAccess('id'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    if (!['admin', 'member'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role. Must be "admin" or "member"'
      });
    }

    // Check if user is admin
    if (!req.targetFamily.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required'
      });
    }

    // Can't demote yourself if you're the only admin
    if (userId === req.user._id.toString() && role === 'member') {
      const adminCount = req.targetFamily.getAdminCount();
      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot demote the only admin. Promote another member to admin first.'
        });
      }
    }

    // Update role in family
    await req.targetFamily.updateMemberRole(userId, role);

    // Update role in user's families list
    const user = await User.findById(userId);
    if (user) {
      const familyMembership = user.families.find(f => 
        f.family.toString() === req.targetFamily._id.toString()
      );
      if (familyMembership) {
        familyMembership.role = role;
        await user.save();
      }
    }

    res.json({
      success: true,
      message: 'Member role updated successfully'
    });
  } catch (error) {
    console.error('Update member role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update member role'
    });
  }
});

// @route   POST /api/families/:id/leave
// @desc    Leave family
// @access  Private (family member)
router.post('/:id/leave', requireFamilyAccess('id'), async (req, res) => {
  try {
    // Check if user is the only admin
    if (req.targetFamily.isAdmin(req.user._id)) {
      const adminCount = req.targetFamily.getAdminCount();
      if (adminCount <= 1 && req.targetFamily.members.length > 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot leave family as the only admin. Promote another member to admin first.'
        });
      }
    }

    // Remove user from family
    await req.targetFamily.removeMember(req.user._id);

    // Remove family from user's families list
    req.user.families = req.user.families.filter(f => 
      f.family.toString() !== req.targetFamily._id.toString()
    );

    // Clear current family if it was this family
    if (req.user.currentFamily && req.user.currentFamily.toString() === req.targetFamily._id.toString()) {
      req.user.currentFamily = req.user.families.length > 0 ? req.user.families[0].family : null;
    }

    await req.user.save();

    res.json({
      success: true,
      message: 'Successfully left family'
    });
  } catch (error) {
    console.error('Leave family error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to leave family'
    });
  }
});

// @route   DELETE /api/families/:id
// @desc    Delete family
// @access  Private (family admin)
router.delete('/:id', requireFamilyAccess('id'), async (req, res) => {
  try {
    // Check if user is admin
    if (!req.targetFamily.isAdmin(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Admin privileges required'
      });
    }

    // Soft delete family
    await Family.findByIdAndUpdate(req.params.id, { isActive: false });

    // Remove family from all members' families lists
    const memberIds = req.targetFamily.members.map(m => m.user);
    await User.updateMany(
      { _id: { $in: memberIds } },
      { 
        $pull: { families: { family: req.targetFamily._id } },
        $unset: { currentFamily: "" }
      }
    );

    // TODO: Handle dogs and entries (soft delete or transfer)

    res.json({
      success: true,
      message: 'Family deleted successfully'
    });
  } catch (error) {
    console.error('Delete family error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete family'
    });
  }
});

module.exports = router;
