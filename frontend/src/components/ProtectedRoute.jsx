import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from './LoadingSpinner';\n\nconst ProtectedRoute = ({ children }) => {\n  const { isAuthenticated, isLoading } = useAuth();\n  const location = useLocation();\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    // Redirect to login page with return url\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;"
