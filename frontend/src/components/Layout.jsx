import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport MobileMenu from './MobileMenu';\n\nconst Layout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user } = useAuth();\n\n  // If user doesn't have a current family, show family selection\n  if (user && !user.currentFamily) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"max-w-md w-full mx-auto p-6\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n              Welcome to Pook! 🐕\n            </h2>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n              You need to create or join a family to start tracking your dog's activities.\n            </p>\n            <div className=\"space-y-3\">\n              <button \n                onClick={() => window.location.href = '/families'}\n                className=\"w-full btn-primary\"\n              >\n                Create or Join Family\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Mobile menu */}\n      <MobileMenu \n        isOpen={sidebarOpen} \n        onClose={() => setSidebarOpen(false)} \n      />\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex flex-col flex-1\">\n        {/* Header */}\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;"
