import React from 'react';\nimport { Loader2 } from 'lucide-react';\n\nconst LoadingSpinner = ({ \n  size = 'md', \n  className = '', \n  text = '',\n  fullScreen = false \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8',\n    xl: 'w-12 h-12',\n  };\n\n  const spinner = (\n    <div className={`flex items-center justify-center ${className}`}>\n      <div className=\"flex flex-col items-center space-y-2\">\n        <Loader2 \n          className={`${sizeClasses[size]} animate-spin text-primary-600`} \n        />\n        {text && (\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {text}\n          </p>\n        )}\n      </div>\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50\">\n        {spinner}\n      </div>\n    );\n  }\n\n  return spinner;\n};\n\nexport default LoadingSpinner;"
