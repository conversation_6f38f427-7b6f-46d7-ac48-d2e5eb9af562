import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { \n  Home, \n  Users, \n  Heart, \n  FileText, \n  BarChart3, \n  Settings,\n  PawPrint\n} from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Dogs', href: '/dogs', icon: Heart },\n  { name: 'Entries', href: '/entries', icon: FileText },\n  { name: 'Statistics', href: '/statistics', icon: BarChart3 },\n  { name: 'Families', href: '/families', icon: Users },\n];\n\nconst Sidebar = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n\n  return (\n    <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 pt-5 pb-4 overflow-y-auto\">\n      {/* Logo */}\n      <div className=\"flex items-center flex-shrink-0 px-4\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            <div className=\"h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n              <PawPrint className=\"h-5 w-5 text-white\" />\n            </div>\n          </div>\n          <div className=\"ml-3\">\n            <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              Pook\n            </h1>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Dog Activity Tracker\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Current family info */}\n      {user?.currentFamily && (\n        <div className=\"mt-6 px-4\">\n          <div className=\"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3\">\n            <p className=\"text-xs font-medium text-primary-600 dark:text-primary-400 uppercase tracking-wide\">\n              Current Family\n            </p>\n            <p className=\"mt-1 text-sm font-medium text-gray-900 dark:text-white truncate\">\n              {user.currentFamily.name}\n            </p>\n            {user.currentFamily.description && (\n              <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400 truncate\">\n                {user.currentFamily.description}\n              </p>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Navigation */}\n      <nav className=\"mt-8 flex-1 px-2 space-y-1\">\n        {navigation.map((item) => {\n          const isActive = location.pathname === item.href;\n          return (\n            <NavLink\n              key={item.name}\n              to={item.href}\n              className={`\n                group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n                ${\n                  isActive\n                    ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-900 dark:text-primary-100'\n                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'\n                }\n              `}\n            >\n              <item.icon\n                className={`\n                  mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200\n                  ${\n                    isActive\n                      ? 'text-primary-600 dark:text-primary-400'\n                      : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'\n                  }\n                `}\n              />\n              {item.name}\n            </NavLink>\n          );\n        })}\n      </nav>\n\n      {/* Bottom section */}\n      <div className=\"flex-shrink-0 px-2\">\n        <NavLink\n          to=\"/profile\"\n          className={`\n            group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200\n            ${\n              location.pathname === '/profile'\n                ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-900 dark:text-primary-100'\n                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'\n            }\n          `}\n        >\n          <Settings\n            className={`\n              mr-3 flex-shrink-0 h-5 w-5 transition-colors duration-200\n              ${\n                location.pathname === '/profile'\n                  ? 'text-primary-600 dark:text-primary-400'\n                  : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'\n              }\n            `}\n          />\n          Settings\n        </NavLink>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;"
