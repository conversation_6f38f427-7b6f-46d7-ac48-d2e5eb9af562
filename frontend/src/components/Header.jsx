import React, { useState, useRef, useEffect } from 'react';\nimport { Menu, Bell, Search, Moon, Sun, User, Settings, LogOut } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { getImageUrl } from '../services/api';\n\nconst Header = ({ onMenuClick }) => {\n  const { user, logout } = useAuth();\n  const { isDarkMode, toggleTheme } = useTheme();\n  const [userMenuOpen, setUserMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const userMenuRef = useRef(null);\n\n  // Close user menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n        setUserMenuOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleLogout = async () => {\n    await logout();\n    setUserMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Left side */}\n          <div className=\"flex items-center\">\n            {/* Mobile menu button */}\n            <button\n              type=\"button\"\n              className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              onClick={onMenuClick}\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n\n            {/* Current family info */}\n            {user?.currentFamily && (\n              <div className=\"ml-4 lg:ml-0\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {user.currentFamily.name}\n                </h1>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                  Family Dashboard\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* Search bar */}\n          <div className=\"hidden md:block flex-1 max-w-lg mx-8\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search entries, dogs...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Theme toggle */}\n            <button\n              onClick={toggleTheme}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n            >\n              {isDarkMode ? (\n                <Sun className=\"h-5 w-5\" />\n              ) : (\n                <Moon className=\"h-5 w-5\" />\n              )}\n            </button>\n\n            {/* Notifications */}\n            <button className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 relative\">\n              <Bell className=\"h-5 w-5\" />\n              {/* Notification badge */}\n              <span className=\"absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white dark:ring-gray-800\"></span>\n            </button>\n\n            {/* User menu */}\n            <div className=\"relative\" ref={userMenuRef}>\n              <button\n                onClick={() => setUserMenuOpen(!userMenuOpen)}\n                className=\"flex items-center space-x-3 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n              >\n                <div className=\"flex-shrink-0\">\n                  {user?.avatar ? (\n                    <img\n                      className=\"h-8 w-8 rounded-full object-cover\"\n                      src={getImageUrl(user.avatar)}\n                      alt={user.name}\n                    />\n                  ) : (\n                    <div className=\"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\">\n                      <User className=\"h-5 w-5 text-white\" />\n                    </div>\n                  )}\n                </div>\n                <div className=\"hidden md:block text-left\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {user?.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {user?.email}\n                  </p>\n                </div>\n              </button>\n\n              {/* User dropdown menu */}\n              {userMenuOpen && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50\">\n                  <div className=\"py-1\">\n                    <a\n                      href=\"/profile\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      onClick={() => setUserMenuOpen(false)}\n                    >\n                      <User className=\"mr-3 h-4 w-4\" />\n                      Profile\n                    </a>\n                    <a\n                      href=\"/families\"\n                      className=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                      onClick={() => setUserMenuOpen(false)}\n                    >\n                      <Settings className=\"mr-3 h-4 w-4\" />\n                      Manage Families\n                    </a>\n                    <hr className=\"my-1 border-gray-200 dark:border-gray-600\" />\n                    <button\n                      onClick={handleLogout}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n                    >\n                      <LogOut className=\"mr-3 h-4 w-4\" />\n                      Sign out\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;"
