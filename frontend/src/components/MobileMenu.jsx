import React, { useEffect } from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { X, Home, Users, Heart, FileText, BarChart3, Settings, PawPrint } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Dogs', href: '/dogs', icon: Heart },\n  { name: 'Entries', href: '/entries', icon: FileText },\n  { name: 'Statistics', href: '/statistics', icon: BarChart3 },\n  { name: 'Families', href: '/families', icon: Users },\n];\n\nconst MobileMenu = ({ isOpen, onClose }) => {\n  const { user } = useAuth();\n  const location = useLocation();\n\n  // Close menu when route changes\n  useEffect(() => {\n    onClose();\n  }, [location.pathname, onClose]);\n\n  // Prevent body scroll when menu is open\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  if (!isOpen) return null;\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div \n        className=\"fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden\"\n        onClick={onClose}\n      />\n\n      {/* Mobile menu */}\n      <div className=\"fixed inset-y-0 left-0 flex flex-col w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 z-50 lg:hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <PawPrint className=\"h-5 w-5 text-white\" />\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Pook\n              </h1>\n            </div>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Current family info */}\n        {user?.currentFamily && (\n          <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3\">\n              <p className=\"text-xs font-medium text-primary-600 dark:text-primary-400 uppercase tracking-wide\">\n                Current Family\n              </p>\n              <p className=\"mt-1 text-sm font-medium text-gray-900 dark:text-white\">\n                {user.currentFamily.name}\n              </p>\n              {user.currentFamily.description && (\n                <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n                  {user.currentFamily.description}\n                </p>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Navigation */}\n        <nav className=\"flex-1 px-2 py-4 space-y-1 overflow-y-auto\">\n          {navigation.map((item) => {\n            const isActive = location.pathname === item.href;\n            return (\n              <NavLink\n                key={item.name}\n                to={item.href}\n                className={`\n                  group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors duration-200\n                  ${\n                    isActive\n                      ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-900 dark:text-primary-100'\n                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'\n                  }\n                `}\n              >\n                <item.icon\n                  className={`\n                    mr-4 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                    ${\n                      isActive\n                        ? 'text-primary-600 dark:text-primary-400'\n                        : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'\n                    }\n                  `}\n                />\n                {item.name}\n              </NavLink>\n            );\n          })}\n        </nav>\n\n        {/* Bottom section */}\n        <div className=\"flex-shrink-0 p-2 border-t border-gray-200 dark:border-gray-700\">\n          <NavLink\n            to=\"/profile\"\n            className={`\n              group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors duration-200\n              ${\n                location.pathname === '/profile'\n                  ? 'bg-primary-100 dark:bg-primary-900/50 text-primary-900 dark:text-primary-100'\n                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'\n              }\n            `}\n          >\n            <Settings\n              className={`\n                mr-4 flex-shrink-0 h-6 w-6 transition-colors duration-200\n                ${\n                  location.pathname === '/profile'\n                    ? 'text-primary-600 dark:text-primary-400'\n                    : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'\n                }\n              `}\n            />\n            Settings\n          </NavLink>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default MobileMenu;"
