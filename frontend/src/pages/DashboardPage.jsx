import React from 'react';\nimport { Heart, FileText, Users, TrendingUp, Plus, Calendar } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { formatDate, getRelativeTime } from '../utils/helpers';\n\nconst DashboardPage = () => {\n  const { user } = useAuth();\n\n  // Mock data - will be replaced with real API calls\n  const stats = {\n    totalDogs: 2,\n    totalEntries: 15,\n    familyMembers: 4,\n    thisWeekEntries: 8\n  };\n\n  const recentEntries = [\n    {\n      id: 1,\n      dog: { name: '<PERSON>', avatar: null },\n      recordedBy: { name: '<PERSON>' },\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago\n      quality: { consistency: 'normal', color: 'brown' }\n    },\n    {\n      id: 2,\n      dog: { name: '<PERSON>', avatar: null },\n      recordedBy: { name: '<PERSON>' },\n      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago\n      quality: { consistency: 'soft', color: 'brown' }\n    }\n  ];\n\n  const StatCard = ({ icon: Icon, title, value, subtitle, color = 'primary' }) => (\n    <div className=\"card p-6\">\n      <div className=\"flex items-center\">\n        <div className={`flex-shrink-0 p-3 rounded-lg bg-${color}-100 dark:bg-${color}-900/50`}>\n          <Icon className={`h-6 w-6 text-${color}-600 dark:text-${color}-400`} />\n        </div>\n        <div className=\"ml-4\">\n          <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n            {title}\n          </p>\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {value}\n          </p>\n          {subtitle && (\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              {subtitle}\n            </p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Welcome back, {user?.name?.split(' ')[0]}! 👋\n          </h1>\n          <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Here's what's happening with your dogs today.\n          </p>\n        </div>\n        <div className=\"mt-4 sm:mt-0\">\n          <button className=\"btn-primary flex items-center\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Add Entry\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          icon={Heart}\n          title=\"Dogs\"\n          value={stats.totalDogs}\n          subtitle=\"in your family\"\n          color=\"primary\"\n        />\n        <StatCard\n          icon={FileText}\n          title=\"Total Entries\"\n          value={stats.totalEntries}\n          subtitle=\"all time\"\n          color=\"secondary\"\n        />\n        <StatCard\n          icon={Users}\n          title=\"Family Members\"\n          value={stats.familyMembers}\n          subtitle=\"active users\"\n          color=\"success\"\n        />\n        <StatCard\n          icon={TrendingUp}\n          title=\"This Week\"\n          value={stats.thisWeekEntries}\n          subtitle=\"entries logged\"\n          color=\"warning\"\n        />\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Entries */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Recent Entries\n            </h2>\n            <a \n              href=\"/entries\" \n              className=\"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\"\n            >\n              View all\n            </a>\n          </div>\n          \n          {recentEntries.length > 0 ? (\n            <div className=\"space-y-4\">\n              {recentEntries.map((entry) => (\n                <div key={entry.id} className=\"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center\">\n                      <Heart className=\"h-5 w-5 text-primary-600 dark:text-primary-400\" />\n                    </div>\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {entry.dog.name}\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      by {entry.recordedBy.name} • {getRelativeTime(entry.timestamp)}\n                    </p>\n                  </div>\n                  <div className=\"flex-shrink-0\">\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      entry.quality.consistency === 'normal' \n                        ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400'\n                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-400'\n                    }`}>\n                      {entry.quality.consistency}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-6\">\n              <FileText className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">\n                No entries yet\n              </h3>\n              <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n                Start tracking your dogs activities.\n              </p>\n              <div className=\"mt-4\">\n                <button className=\"btn-primary btn-sm\">\n                  <Plus className=\"h-4 w-4 mr-1\" />\n                  Add First Entry\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"card p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            Quick Actions\n          </h2>\n          \n          <div className=\"space-y-3\">\n            <a\n              href=\"/entries\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200\"\n            >\n              <div className=\"flex-shrink-0\">\n                <Plus className=\"h-5 w-5 text-primary-600 dark:text-primary-400\" />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Add New Entry\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Record a new activity\n                </p>\n              </div>\n            </a>\n            \n            <a\n              href=\"/dogs\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200\"\n            >\n              <div className=\"flex-shrink-0\">\n                <Heart className=\"h-5 w-5 text-primary-600 dark:text-primary-400\" />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Manage Dogs\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  View and edit dog profiles\n                </p>\n              </div>\n            </a>\n            \n            <a\n              href=\"/statistics\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200\"\n            >\n              <div className=\"flex-shrink-0\">\n                <TrendingUp className=\"h-5 w-5 text-primary-600 dark:text-primary-400\" />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  View Statistics\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Analyze activity patterns\n                </p>\n              </div>\n            </a>\n            \n            <a\n              href=\"/families\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200\"\n            >\n              <div className=\"flex-shrink-0\">\n                <Users className=\"h-5 w-5 text-primary-600 dark:text-primary-400\" />\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Family Settings\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Manage family members\n                </p>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* Today's Schedule */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Today's Schedule\n          </h2>\n          <Calendar className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        \n        <div className=\"text-center py-8\">\n          <Calendar className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">\n            No scheduled activities\n          </h3>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Your dogs are all set for today!\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardPage;"
