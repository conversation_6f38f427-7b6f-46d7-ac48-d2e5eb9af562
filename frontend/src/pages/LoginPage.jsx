import React, { useEffect } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { <PERSON>w<PERSON><PERSON>t, Heart, Users, BarChart3 } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst LoginPage = () => {\n  const { isAuthenticated, isLoading, loginWithGoogle, error, clearError } = useAuth();\n\n  useEffect(() => {\n    // Clear any existing errors when component mounts\n    if (error) {\n      clearError();\n    }\n  }, [error, clearError]);\n\n  // Redirect if already authenticated\n  if (isAuthenticated) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  if (isLoading) {\n    return <LoadingSpinner fullScreen text=\"Checking authentication...\" />;\n  }\n\n  const features = [\n    {\n      icon: Heart,\n      title: 'Track Your Dogs',\n      description: 'Monitor your furry friends health and activities with detailed profiles and medical information.'\n    },\n    {\n      icon: Users,\n      title: 'Family Collaboration',\n      description: 'Share responsibilities with family members and keep everyone informed about your pets wellbeing.'\n    },\n    {\n      icon: BarChart3,\n      title: 'Detailed Analytics',\n      description: 'Get insights into patterns and trends to better understand your dogs health and behavior.'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-gray-900 dark:to-gray-800 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        {/* Logo */}\n        <div className=\"flex justify-center\">\n          <div className=\"flex items-center\">\n            <div className=\"h-12 w-12 bg-primary-600 rounded-xl flex items-center justify-center\">\n              <PawPrint className=\"h-8 w-8 text-white\" />\n            </div>\n            <div className=\"ml-3\">\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                Pook\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Dog Activity Tracker\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Welcome message */}\n        <div className=\"mt-8 text-center\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            Welcome to Pook! 🐕\n          </h2>\n          <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n            The modern way to track your dogs health and activities with your family.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white dark:bg-gray-800 py-8 px-4 shadow-soft sm:rounded-lg sm:px-10\">\n          {/* Error message */}\n          {error && (\n            <div className=\"mb-6 bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n              <p className=\"text-sm text-red-600 dark:text-red-400\">\n                {error}\n              </p>\n            </div>\n          )}\n\n          {/* Login button */}\n          <div>\n            <button\n              onClick={loginWithGoogle}\n              className=\"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200\"\n            >\n              <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                <path\n                  fill=\"currentColor\"\n                  d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                />\n              </svg>\n              Continue with Google\n            </button>\n          </div>\n\n          {/* Features */}\n          <div className=\"mt-8\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300 dark:border-gray-600\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400\">\n                  Why choose Pook?\n                </span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 space-y-4\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"flex items-start\">\n                  <div className=\"flex-shrink-0\">\n                    <feature.icon className=\"h-5 w-5 text-primary-600 dark:text-primary-400\" />\n                  </div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"mt-8 text-center\">\n        <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n          By signing in, you agree to our terms of service and privacy policy.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;"
