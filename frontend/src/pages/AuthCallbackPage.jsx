import React, { useEffect, useState } from 'react';\nimport { Navigate, useSearchParams } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from '../components/LoadingSpinner';\n\nconst AuthCallbackPage = () => {\n  const [searchParams] = useSearchParams();\n  const { loginWithToken, isAuthenticated } = useAuth();\n  const [error, setError] = useState(null);\n  const [isProcessing, setIsProcessing] = useState(true);\n\n  useEffect(() => {\n    const handleCallback = async () => {\n      try {\n        const token = searchParams.get('token');\n        const errorParam = searchParams.get('error');\n\n        if (errorParam) {\n          setError('Authentication failed. Please try again.');\n          setIsProcessing(false);\n          return;\n        }\n\n        if (!token) {\n          setError('No authentication token received.');\n          setIsProcessing(false);\n          return;\n        }\n\n        const result = await loginWithToken(token);\n        \n        if (!result.success) {\n          setError(result.error || 'Authentication failed.');\n        }\n      } catch (err) {\n        console.error('Auth callback error:', err);\n        setError('An unexpected error occurred during authentication.');\n      } finally {\n        setIsProcessing(false);\n      }\n    };\n\n    handleCallback();\n  }, [searchParams, loginWithToken]);\n\n  // Redirect if authenticated\n  if (isAuthenticated) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"max-w-md w-full mx-auto p-6\">\n          <div className=\"text-center\">\n            <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50\">\n              <svg className=\"h-6 w-6 text-red-600 dark:text-red-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </div>\n            <h2 className=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">\n              Authentication Failed\n            </h2>\n            <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n              {error}\n            </p>\n            <div className=\"mt-6\">\n              <a\n                href=\"/login\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n              >\n                Try Again\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show loading state\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n      <div className=\"text-center\">\n        <LoadingSpinner size=\"xl\" text=\"Completing authentication...\" />\n        <p className=\"mt-4 text-sm text-gray-600 dark:text-gray-400\">\n          Please wait while we set up your account.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthCallbackPage;"
