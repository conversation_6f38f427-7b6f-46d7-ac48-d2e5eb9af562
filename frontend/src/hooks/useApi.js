import { useState, useEffect, useCallback } from 'react';
import { handleAPIError } from '../services/api';

export const useApi = (apiFunction, dependencies = [], options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const {
    immediate = true,
    onSuccess,
    onError,
    transform,
  } = options;

  const execute = useCallback(async (...args) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiFunction(...args);
      let result = response.data;

      // Transform data if transform function provided
      if (transform) {
        result = transform(result);
      }

      setData(result);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      return { success: true, data: result };
    } catch (err) {
      const errorInfo = handleAPIError(err);
      setError(errorInfo);

      // Call error callback if provided
      if (onError) {
        onError(errorInfo);
      }

      return { success: false, error: errorInfo };
    } finally {
      setLoading(false);
    }
  }, [apiFunction, transform, onSuccess, onError]);

  // Execute immediately if immediate is true
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, dependencies);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset,
  };
};

export const useMutation = (apiFunction, options = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const {
    onSuccess,
    onError,
    transform,
  } = options;

  const mutate = useCallback(async (...args) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiFunction(...args);
      let result = response.data;

      // Transform data if transform function provided
      if (transform) {
        result = transform(result);
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      return { success: true, data: result };
    } catch (err) {
      const errorInfo = handleAPIError(err);
      setError(errorInfo);

      // Call error callback if provided
      if (onError) {
        onError(errorInfo);
      }

      return { success: false, error: errorInfo };
    } finally {
      setLoading(false);
    }
  }, [apiFunction, transform, onSuccess, onError]);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return {
    mutate,
    loading,
    error,
    reset,
  };
};
