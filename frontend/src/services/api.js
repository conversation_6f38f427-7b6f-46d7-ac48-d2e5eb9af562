import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  // Get current user
  getMe: () => api.get('/auth/me'),
  
  // Update user profile
  updateProfile: (data) => api.put('/auth/me', data),
  
  // Switch family
  switchFamily: (familyId) => api.post('/auth/switch-family', { familyId }),
  
  // Logout
  logout: () => api.post('/auth/logout'),
  
  // Delete account
  deleteAccount: (confirmEmail) => api.delete('/auth/account', { data: { confirmEmail } }),
};

// Family API
export const familyAPI = {
  // Get all families
  getFamilies: () => api.get('/families'),
  
  // Create family
  createFamily: (data) => api.post('/families', data),
  
  // Get family details
  getFamily: (id) => api.get(`/families/${id}`),
  
  // Update family
  updateFamily: (id, data) => api.put(`/families/${id}`, data),
  
  // Delete family
  deleteFamily: (id) => api.delete(`/families/${id}`),
  
  // Join family
  joinFamily: (inviteCode) => api.post('/families/join', { inviteCode }),
  
  // Generate invite code
  generateInviteCode: (id) => api.post(`/families/${id}/invite`),
  
  // Remove member
  removeMember: (familyId, userId) => api.delete(`/families/${familyId}/members/${userId}`),
  
  // Update member role
  updateMemberRole: (familyId, userId, role) => 
    api.put(`/families/${familyId}/members/${userId}/role`, { role }),
  
  // Leave family
  leaveFamily: (id) => api.post(`/families/${id}/leave`),
};

// Dog API
export const dogAPI = {
  // Get all dogs
  getDogs: () => api.get('/dogs'),
  
  // Create dog
  createDog: (data) => api.post('/dogs', data),
  
  // Get dog details
  getDog: (id) => api.get(`/dogs/${id}`),
  
  // Update dog
  updateDog: (id, data) => api.put(`/dogs/${id}`, data),
  
  // Delete dog
  deleteDog: (id) => api.delete(`/dogs/${id}`),
  
  // Get dog statistics
  getDogStatistics: (id, days = 30) => api.get(`/dogs/${id}/statistics?days=${days}`),
  
  // Get dog entries
  getDogEntries: (id, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/dogs/${id}/entries?${queryString}`);
  },
};

// Entry API
export const entryAPI = {
  // Get all entries
  getEntries: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/entries?${queryString}`);
  },
  
  // Create entry
  createEntry: (data) => api.post('/entries', data),
  
  // Get entry details
  getEntry: (id) => api.get(`/entries/${id}`),
  
  // Update entry
  updateEntry: (id, data) => api.put(`/entries/${id}`, data),
  
  // Delete entry
  deleteEntry: (id) => api.delete(`/entries/${id}`),
  
  // Approve entry
  approveEntry: (id) => api.post(`/entries/${id}/approve`),
  
  // Get family statistics
  getFamilyStatistics: (days = 30) => api.get(`/entries/statistics/family?days=${days}`),
  
  // Get pending entries
  getPendingEntries: () => api.get('/entries/pending'),
};

// Upload API
export const uploadAPI = {
  // Upload images
  uploadImages: (files) => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('images', file);
    });
    
    return api.post('/upload/images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Upload avatar
  uploadAvatar: (file) => {
    const formData = new FormData();
    formData.append('avatar', file);
    
    return api.post('/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Delete file
  deleteFile: (familyId, filename) => api.delete(`/upload/${familyId}/${filename}`),
  
  // Get file info
  getFileInfo: (familyId, filename) => api.get(`/upload/info/${familyId}/${filename}`),
  
  // Get family files
  getFamilyFiles: () => api.get('/upload/family-files'),
};

// Helper function to handle API errors
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    return {
      message: error.response.data?.message || 'An error occurred',
      status: error.response.status,
      data: error.response.data,
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error. Please check your connection.',
      status: 0,
    };
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: -1,
    };
  }
};

// Helper function to get image URL
export const getImageUrl = (path) => {
  if (!path) return null;
  if (path.startsWith('http')) return path;
  const baseURL = import.meta.env.VITE_API_URL || 'http://localhost:5000';
  return `${baseURL}${path}`;
};

export default api;
